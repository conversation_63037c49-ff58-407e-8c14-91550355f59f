import * as React from 'react';
interface ContactFormEmailProps {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export const ContactFormEmail: React.FC<Readonly<ContactFormEmailProps>> = ({
  name,
  email,
  subject,
  message
}) => (
  <html lang='en'>
    <body style={{ fontFamily: 'Arial, sans-serif', lineHeight: '1.6', color: '#333' }}>
      <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
        <h1 style={{ color: '#2563eb', borderBottom: '2px solid #2563eb', paddingBottom: '10px' }}>
          🎉 Someone Actually Used the Contact Form!
        </h1>
        <p style={{ fontSize: '16px', marginBottom: '20px' }}>
          Hey Darjus! Someone reached out through the FAAFO contact form.
          Time to put down the coffee and actually respond to people ☕
        </p>

        <div style={{ backgroundColor: '#f8f9fa', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
          <p style={{ margin: '10px 0' }}>
            <strong>Who:</strong> {name}
          </p>
          <p style={{ margin: '10px 0' }}>
            <strong>Email:</strong> {email}
          </p>
          <p style={{ margin: '10px 0' }}>
            <strong>Subject:</strong> {subject}
          </p>
        </div>

        <div style={{ backgroundColor: '#fff3cd', padding: '20px', borderRadius: '8px', border: '1px solid #ffeaa7' }}>
          <p style={{ margin: '0 0 10px 0' }}>
            <strong>Their Message:</strong>
          </p>
          <p style={{ margin: '0', fontStyle: 'italic' }}>{message}</p>
        </div>

        <p style={{ fontSize: '14px', color: '#666', marginTop: '20px' }}>
          <em>P.S. - Don't forget to actually reply this time! Remember what happened with the last 47 emails... 😅</em>
        </p>
      </div>
    </body>
  </html>
);
