import * as React from 'react';
import { Html, Head, Body, Container, Text, Button } from '@react-email/components';
interface VerificationEmailProps {
  username: string;
  verificationLink: string;
}

export const VerificationEmail = ({ username, verificationLink }: VerificationEmailProps) => (
  <Html>
    <Head>
      <style>{`
        body {
          background-color: white;
          margin: auto;
          font-family: sans-serif;
          padding: 8px;
        }
        .container {
          border: 1px solid #eaeaea;
          border-radius: 8px;
          margin: 32px auto;
          padding: 32px;
          max-width: 600px;
        }
        .title {
          color: black;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 16px;
        }
        .text {
          color: black;
          font-size: 16px;
          line-height: 1.5;
          margin-bottom: 16px;
        }
        .button {
          background-color: #1f2937;
          border-radius: 6px;
          color: white;
          font-size: 16px;
          font-weight: 600;
          text-decoration: none;
          text-align: center;
          padding: 12px 20px;
          display: inline-block;
          margin: 16px 0;
        }
      `}</style>
    </Head>
    <Body>
      <Container className='container'>
        <Text className='title'>🎉 Welcome to FAAFO! (F*** Around and Find Out)</Text>
        <Text className='text'>Hey {username}!</Text>
        <Text className='text'>
          So you decided to join the FAAFO adventure - excellent choice! 🚀
          You're about to embark on a journey to f*** around with your career and find out what actually works.
        </Text>
        <Text className='text'>
          But first, I need to make sure you're a real human and not some bot trying to infiltrate
          our little rebellion. Click the button below to verify your email:
        </Text>
        <Button className='button' href={verificationLink}>
          Yes, I'm Ready to FAAFO! ✊
        </Button>
        <Text className='text'>
          <strong>Fair warning:</strong> This platform has bugs, I'm still figuring things out,
          and I built most of it at 3 AM with coffee and questionable life choices.
          But hey, at least we're honest about it! )))
        </Text>
        <Text className='text'>
          If you didn't sign up for this chaos, just ignore this email and carry on with your
          perfectly normal, soul-crushing routine. No judgment here! 😅
        </Text>
        <Text className='text'>
          Welcome to the experiment,
          <br />
          Darjus (Solo Founder & Chief Coffee Consumer) ☕
          <br />
          <em>P.S. - I actually read emails, so feel free to reply and say hi!</em>
        </Text>
      </Container>
    </Body>
  </Html>
);

export default VerificationEmail;
