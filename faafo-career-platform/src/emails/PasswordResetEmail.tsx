import React from 'react';
import { Html, Head, Body, Container, Text, Link, Img } from '@react-email/components';
// Force deployment trigger
interface PasswordResetEmailProps {
  resetLink: string;
}

export const PasswordResetEmail = ({ resetLink }: PasswordResetEmailProps) => (
  <Html lang='en'>
    <Head />
    <Body style={main}>
      <Container style={container}>
        <Text style={paragraph}>Hey there, fellow career rebel! 👋</Text>
        <Text style={paragraph}>
          So you forgot your password? Don't worry, it happens to the best of us.
          I once forgot my own password to this platform... that I built... while I was logged in 🤦‍♂️
        </Text>
        <Text style={paragraph}>
          Click the link below to reset your password and get back to f***ing around with your career:
        </Text>
        <Text style={paragraph}>
          <Link href={resetLink} style={linkStyle}>
            Reset My Password (I Promise to Remember This Time)
          </Link>
        </Text>
        <Text style={paragraph}>
          If you didn't request this reset, someone probably just fat-fingered your email address.
          Just ignore this email and carry on with your day. No harm, no foul!
        </Text>
        <Text style={paragraph}>
          If you're having trouble, just reply to this email. I actually read them
          (usually with coffee in hand) ☕
        </Text>
        <Text style={paragraph}>Stay rebellious,</Text>
        <Text style={paragraph}>
          Darjus<br />
          <em>Solo Developer & Professional Password Forgetter</em>
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif'
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px'
};

const logo = {
  margin: '0 auto'
};

const paragraph = {
  color: '#444',
  fontSize: '15px',
  lineHeight: '24px',
  textAlign: 'left' as const
};

const linkStyle = {
  color: '#0066cc',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'underline',
  wordBreak: 'break-all' as const
};
