'use client';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Heart, ArrowRight } from 'lucide-react';

interface SubscriptionDetails {
  // Simplified interface for compatibility
  id?: string;
  tier?: string;
}

interface CustomBillingPortalProps {
  subscriptionDetails: SubscriptionDetails;
}

export default function CustomBillingPortal({ subscriptionDetails }: CustomBillingPortalProps) {
  return (
    <Card>
      <CardHeader>
        <div className='inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4 mx-auto'>
          <Heart className='h-8 w-8 text-green-600' />
        </div>
        <CardTitle className='text-center text-green-600'>No Billing to Manage!</CardTitle>
      </CardHeader>
      <CardContent className='text-center'>
        <p className='text-gray-600 dark:text-gray-400 mb-6'>
          All features of the FAAFO Career Platform are completely free for everyone.
          There's no subscription to manage!
        </p>
        <div className='space-y-3'>
          <Button asChild size='lg' className='w-full'>
            <a href='/dashboard'>
              Go to Dashboard
              <ArrowRight className='ml-2 h-4 w-4' />
            </a>
          </Button>
          <Button variant='outline' size='lg' asChild className='w-full'>
            <a href='/subscription'>Learn More</a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
