'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Crown, Zap, Star, AlertCircle } from 'lucide-react';
import Link from 'next/link';

interface SubscriptionData {
  hasAccess: boolean;
  subscription?: {
    tier: string;
    status: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
  };
  upgradeUrl?: string;
  message?: string;
}

export function SubscriptionStatus() {
  // All features are now free - no subscription status needed
  return null;
}

export function SubscriptionStatusMobile() {
  // All features are now free - no subscription status needed
  return null;
}
