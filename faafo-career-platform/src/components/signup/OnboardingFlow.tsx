'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Rocket,
  Heart,
  Target,
  Users,
  DollarSign,
  BookOpen,
  Coffee,
  Lightbulb,
  ArrowRight,
  CheckCircle,
  Star
} from 'lucide-react';
import CoffeeFundTracker from '@/components/ui/CoffeeFundTracker';

interface OnboardingStepProps {
  title: string;
  description: string;
  children: React.ReactNode;
}

function OnboardingStep({ title, description, children }: OnboardingStepProps) {
  return (
    <Card className='w-full max-w-md'>
      <CardHeader>
        <CardTitle className='text-2xl'>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}

export default function OnboardingFlow() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 7; // Comprehensive onboarding journey

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      // Onboarding completed, navigate to dashboard or home
      router.push('/dashboard');
    }
  };

  const handleStartAssessment = () => {
    router.push('/assessment');
  };

  const handleSkip = () => {
    router.push('/dashboard'); // Or a less intrusive path
  };

  const getStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <div className='mx-auto mb-4 w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center'>
                <Coffee className='w-10 h-10 text-white' />
              </div>
              <CardTitle className='text-3xl font-bold'>Hey there! 👋</CardTitle>
              <CardDescription className='text-lg'>
                Welcome to the FAAFO Career Platform
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 p-6 rounded-lg'>
                <div className='flex items-start gap-4'>
                  <div className='w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0'>
                    <span className='text-white font-bold text-lg'>D</span>
                  </div>
                  <div>
                    <h3 className='font-semibold text-lg mb-2'>Hi, I'm Darjus! 🚀</h3>
                    <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                      I'm the solo founder, developer, and chief coffee consumer behind this platform.
                      I'm not some tech guru or successful entrepreneur - just a guy who's had too many
                      soul-crushing jobs (still working in one, actually 😅).
                    </p>
                    <p className='text-gray-700 dark:text-gray-300 leading-relaxed mt-3'>
                      After hearing everyone talk about AI, I thought "maybe I can build something?"
                      Spoiler alert: I tried many times and failed. A lot. But this one felt different -
                      maybe a little bit meaningful? I guess we'll find out )))
                    </p>
                  </div>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <div className='text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg'>
                  <Target className='w-8 h-8 text-green-600 mx-auto mb-2' />
                  <h4 className='font-semibold'>Find Your Path</h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>Discover careers that actually excite you</p>
                </div>
                <div className='text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg'>
                  <DollarSign className='w-8 h-8 text-blue-600 mx-auto mb-2' />
                  <h4 className='font-semibold'>Build Freedom</h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>Plan your financial independence</p>
                </div>
                <div className='text-center p-4 bg-purple-50 dark:bg-purple-950 rounded-lg'>
                  <Users className='w-8 h-8 text-purple-600 mx-auto mb-2' />
                  <h4 className='font-semibold'>Join Community</h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>Connect with fellow career changers</p>
                </div>
              </div>

              <div className='flex justify-between items-center'>
                <Button variant='outline' onClick={handleSkip} className='flex items-center gap-2'>
                  Skip Tour
                </Button>
                <Button onClick={handleNext} className='flex items-center gap-2'>
                  Let's Go! <ArrowRight className='w-4 h-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      case 2:
        return (
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <Lightbulb className='w-16 h-16 text-yellow-500 mx-auto mb-4' />
              <CardTitle className='text-2xl font-bold'>What is FAAFO? 🤔</CardTitle>
              <CardDescription>
                Let me explain what we're all about (and why the name makes people chuckle)
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='bg-yellow-50 dark:bg-yellow-950 p-6 rounded-lg border-l-4 border-yellow-400'>
                <h3 className='font-bold text-lg mb-3'>FAAFO = "Find Another Alternative, Find Opportunities" 🎯</h3>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mb-3'>
                  Okay, fine... let's be real here. FAAFO actually stands for "F*** Around and Find Out" 😏
                  Because honestly? I wanted to f*** around and find out if it's actually possible to
                  build something that helps people escape soul-crushing jobs.
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                  Spoiler alert: still finding out, but hey, at least we're having fun with it )))
                  That rebellious "f*** it, let's try something different" spirit is exactly what career change needs!
                  Sometimes you have to say "forget this" to your current situation to find something better.
                </p>
              </div>

              <div className='space-y-4'>
                <h3 className='font-semibold text-lg'>Here's what we're really about:</h3>

                <div className='space-y-3'>
                  <div className='flex items-start gap-3'>
                    <CheckCircle className='w-5 h-5 text-green-500 mt-1 flex-shrink-0' />
                    <div>
                      <strong>Career Assessment:</strong> Not your typical "what's your favorite color" quiz.
                      We dig deep into your skills, values, and what actually makes you tick.
                    </div>
                  </div>

                  <div className='flex items-start gap-3'>
                    <CheckCircle className='w-5 h-5 text-green-500 mt-1 flex-shrink-0' />
                    <div>
                      <strong>Freedom Fund Calculator:</strong> Because "follow your passion" is terrible advice
                      if you can't pay rent. We help you plan the financial bridge to your new career.
                    </div>
                  </div>

                  <div className='flex items-start gap-3'>
                    <CheckCircle className='w-5 h-5 text-green-500 mt-1 flex-shrink-0' />
                    <div>
                      <strong>Real Community:</strong> Connect with people who get it. No toxic positivity,
                      just honest support from folks on the same journey.
                    </div>
                  </div>

                  <div className='flex items-start gap-3'>
                    <CheckCircle className='w-5 h-5 text-green-500 mt-1 flex-shrink-0' />
                    <div>
                      <strong>Practical Resources:</strong> Curated tools, courses, and guides that actually work.
                      No fluff, no get-rich-quick schemes.
                    </div>
                  </div>
                </div>
              </div>

              <div className='flex justify-between items-center'>
                <Button variant='outline' onClick={() => setCurrentStep(currentStep - 1)}>
                  Back
                </Button>
                <Button onClick={handleNext} className='flex items-center gap-2'>
                  Got it! <ArrowRight className='w-4 h-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      case 3:
        return (
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <Heart className='w-16 h-16 text-red-500 mx-auto mb-4' />
              <CardTitle className='text-2xl font-bold'>Why I Built This 💝</CardTitle>
              <CardDescription>
                The personal story behind FAAFO (grab some tissues, it gets real)
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='bg-red-50 dark:bg-red-950 p-6 rounded-lg'>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mb-4'>
                  <strong>Real talk:</strong> I'm not building this from some ivory tower. I'm still stuck in a job I hate,
                  dealing with the same Sunday scaries and 5 PM countdowns as you. The difference is I decided to
                  do something about it (even though I have no idea what I'm doing most of the time).
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mb-4'>
                  I tried to sell this platform - no one wanted to buy it 😂😂😂 So here we are)))
                  Everything's free because honestly, I'm broke and losing money on this anyway.
                  But hey, if it helps you escape the 9-to-5 hell, maybe it's worth it?
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                  This is a solo project built by someone who's still figuring it out. No fancy team,
                  no investors, just me, my laptop, and way too much caffeine. The good news? I'm constantly
                  working on making this better - new features, bug fixes, and random improvements happen
                  whenever I get inspired (usually during late-night coding sessions).
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mt-3'>
                  If you feel generous, coffee donations are appreciated but totally not required.
                  I'll keep this free and keep developing it either way because I'm apparently terrible
                  at business but weirdly passionate about helping people escape job hell 🤷‍♂️
                  <br />
                  <a href="https://ko-fi.com/faafo" target="_blank" rel="noopener noreferrer" className='text-blue-600 hover:text-blue-500 underline'>
                    ☕ Support the coffee fund
                  </a>
                </p>
              </div>

              <div className='bg-yellow-50 dark:bg-yellow-950 p-6 rounded-lg border-l-4 border-yellow-400 mb-6'>
                <h4 className='font-semibold mb-2 text-yellow-800 dark:text-yellow-200'>⚠️ Fair Warning: This Platform Has Issues</h4>
                <p className='text-yellow-700 dark:text-yellow-300 leading-relaxed mb-3'>
                  <strong>Real talk:</strong> This platform has more bugs than a summer camping trip.
                  Sometimes buttons don't work, pages load weird, and the AI occasionally gives advice
                  that makes no sense (just like real career counselors, honestly).
                </p>
                <p className='text-yellow-700 dark:text-yellow-300 leading-relaxed mb-3'>
                  I lost my patience (and probably my sanity) building this thing at 3 AM with nothing
                  but coffee, weed, and questionable life choices. There's no QA team - it's just me,
                  frantically testing things and hoping they work )))
                </p>
                <p className='text-yellow-700 dark:text-yellow-300 leading-relaxed mb-3'>
                  <strong>So please:</strong> Have patience when something breaks (and it will).
                  Refresh the page, try again, maybe take a deep breath. If it's really broken,
                  email me and I'll fix it... eventually... after I finish banging my head against the wall 😅
                </p>
                <p className='text-yellow-700 dark:text-yellow-300 leading-relaxed'>
                  <strong>The good news:</strong> This thing is constantly evolving! I'm always adding new features,
                  fixing bugs (creating new ones in the process), and trying to make it less terrible.
                  So what's broken today might work tomorrow, and what works today might break tomorrow )))
                  It's like a career change - unpredictable but hopefully trending upward!
                </p>
              </div>

              <div className='flex justify-center mb-6'>
                <CoffeeFundTracker />
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='p-4 bg-blue-50 dark:bg-blue-950 rounded-lg'>
                  <h4 className='font-semibold mb-2'>🎯 What We Believe</h4>
                  <ul className='text-sm space-y-1 text-gray-600 dark:text-gray-400'>
                    <li>• Career change is possible at any age</li>
                    <li>• Financial planning isn't boring (okay, maybe a little)</li>
                    <li>• Community beats going it alone</li>
                    <li>• Small steps lead to big changes</li>
                  </ul>
                </div>
                <div className='p-4 bg-green-50 dark:bg-green-950 rounded-lg'>
                  <h4 className='font-semibold mb-2'>🚫 What We Don't Do</h4>
                  <ul className='text-sm space-y-1 text-gray-600 dark:text-gray-400'>
                    <li>• Promise overnight success</li>
                    <li>• Ignore financial reality</li>
                    <li>• Give generic advice</li>
                    <li>• Judge your current situation</li>
                  </ul>
                </div>
              </div>

              <div className='flex justify-between items-center'>
                <Button variant='outline' onClick={() => setCurrentStep(currentStep - 1)}>
                  Back
                </Button>
                <Button onClick={handleNext} className='flex items-center gap-2'>
                  I'm Ready! <ArrowRight className='w-4 h-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      case 4:
        return (
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <BookOpen className='w-16 h-16 text-blue-500 mx-auto mb-4' />
              <CardTitle className='text-2xl font-bold'>Your Career Assessment 📋</CardTitle>
              <CardDescription>
                The foundation of your personalized career journey
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='bg-blue-50 dark:bg-blue-950 p-6 rounded-lg'>
                <h3 className='font-semibold text-lg mb-3'>This isn't your typical career quiz 🎯</h3>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mb-4'>
                  Forget "What's your spirit animal?" We're going deeper. Our assessment looks at:
                </p>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <div className='flex items-center gap-2'>
                      <Star className='w-4 h-4 text-yellow-500' />
                      <span className='font-medium'>Your Skills</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Star className='w-4 h-4 text-yellow-500' />
                      <span className='font-medium'>Your Values</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Star className='w-4 h-4 text-yellow-500' />
                      <span className='font-medium'>Your Interests</span>
                    </div>
                  </div>
                  <div className='space-y-2'>
                    <div className='flex items-center gap-2'>
                      <Star className='w-4 h-4 text-yellow-500' />
                      <span className='font-medium'>Work Style</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Star className='w-4 h-4 text-yellow-500' />
                      <span className='font-medium'>Risk Tolerance</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Star className='w-4 h-4 text-yellow-500' />
                      <span className='font-medium'>Life Goals</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className='bg-yellow-50 dark:bg-yellow-950 p-4 rounded-lg border-l-4 border-yellow-400'>
                <p className='text-sm'>
                  <strong>Pro tip:</strong> Be honest! The algorithm can't help you if you're trying to be
                  the "perfect" candidate. We're here to find what actually fits YOU, not what sounds impressive.
                </p>
              </div>

              <div className='flex justify-between items-center'>
                <Button variant='outline' onClick={() => setCurrentStep(currentStep - 1)}>
                  Back
                </Button>
                <Button onClick={handleStartAssessment} className='flex items-center gap-2'>
                  Start Assessment <Target className='w-4 h-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      case 5:
        return (
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <DollarSign className='w-16 h-16 text-green-500 mx-auto mb-4' />
              <CardTitle className='text-2xl font-bold'>Your Freedom Fund 💰</CardTitle>
              <CardDescription>
                Because "follow your passion" doesn't pay the bills
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='bg-green-50 dark:bg-green-950 p-6 rounded-lg'>
                <h3 className='font-semibold text-lg mb-3'>Let's talk money (the elephant in the room) 🐘</h3>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mb-4'>
                  Here's what nobody tells you about career change: it's not just about finding your passion.
                  You need a financial bridge to get from where you are to where you want to be.
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                  The Freedom Fund isn't just savings - it's your "F*** You Fund" that gives you the power
                  to make choices based on what you want, not just what you need to survive.
                </p>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <div className='text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg'>
                  <div className='text-2xl font-bold text-blue-600'>3-6</div>
                  <div className='text-sm'>Months of expenses</div>
                </div>
                <div className='text-center p-4 bg-purple-50 dark:bg-purple-950 rounded-lg'>
                  <div className='text-2xl font-bold text-purple-600'>+</div>
                  <div className='text-sm'>Transition costs</div>
                </div>
                <div className='text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg'>
                  <div className='text-2xl font-bold text-green-600'>=</div>
                  <div className='text-sm'>Career freedom</div>
                </div>
              </div>

              <div className='bg-yellow-50 dark:bg-yellow-950 p-4 rounded-lg'>
                <p className='text-sm'>
                  <strong>Don't panic!</strong> We'll help you calculate exactly how much you need and create
                  a realistic plan to get there. No judgment, just math and strategy.
                </p>
              </div>

              <div className='flex justify-between items-center'>
                <Button variant='outline' onClick={() => setCurrentStep(currentStep - 1)}>
                  Back
                </Button>
                <Button onClick={handleNext} className='flex items-center gap-2'>
                  Show Me How <ArrowRight className='w-4 h-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      case 6:
        return (
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <Users className='w-16 h-16 text-purple-500 mx-auto mb-4' />
              <CardTitle className='text-2xl font-bold'>Join the Community 🤝</CardTitle>
              <CardDescription>
                Because career change is better with friends (and fewer existential crises)
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='bg-purple-50 dark:bg-purple-950 p-6 rounded-lg'>
                <h3 className='font-semibold text-lg mb-3'>You're not alone in this journey 🫂</h3>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mb-4'>
                  Career change can feel isolating. Your current colleagues might not get it,
                  your family might worry, and your friends might think you're having a midlife crisis
                  (even if you're 25).
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                  Our community is full of people who understand the struggle, celebrate the wins,
                  and won't judge you for stress-eating ice cream while updating your LinkedIn profile at midnight.
                </p>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='p-4 bg-blue-50 dark:bg-blue-950 rounded-lg'>
                  <h4 className='font-semibold mb-2 flex items-center gap-2'>
                    <BookOpen className='w-4 h-4' />
                    Share & Learn
                  </h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>
                    Ask questions, share resources, and learn from others' experiences
                  </p>
                </div>
                <div className='p-4 bg-green-50 dark:bg-green-950 rounded-lg'>
                  <h4 className='font-semibold mb-2 flex items-center gap-2'>
                    <Heart className='w-4 h-4' />
                    Support System
                  </h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>
                    Get encouragement when you need it most (and give it back)
                  </p>
                </div>
                <div className='p-4 bg-yellow-50 dark:bg-yellow-950 rounded-lg'>
                  <h4 className='font-semibold mb-2 flex items-center gap-2'>
                    <Lightbulb className='w-4 h-4' />
                    Real Stories
                  </h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>
                    Read about actual career transitions, not just success stories
                  </p>
                </div>
                <div className='p-4 bg-red-50 dark:bg-red-950 rounded-lg'>
                  <h4 className='font-semibold mb-2 flex items-center gap-2'>
                    <Target className='w-4 h-4' />
                    Accountability
                  </h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>
                    Find accountability partners to keep you moving forward
                  </p>
                </div>
              </div>

              <div className='bg-yellow-50 dark:bg-yellow-950 p-4 rounded-lg border-l-4 border-yellow-400'>
                <p className='text-sm'>
                  <strong>Community Guidelines:</strong> Be kind, be real, be supportive.
                  No spam, no toxic positivity, no "just quit your job and travel the world" advice
                  (unless you're also sharing your trust fund details).
                </p>
              </div>

              <div className='flex justify-between items-center'>
                <Button variant='outline' onClick={() => setCurrentStep(currentStep - 1)}>
                  Back
                </Button>
                <Button onClick={handleNext} className='flex items-center gap-2'>
                  I'm In! <ArrowRight className='w-4 h-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      case 7:
        return (
          <Card className='w-full max-w-2xl'>
            <CardHeader className='text-center'>
              <Rocket className='w-16 h-16 text-green-500 mx-auto mb-4' />
              <CardTitle className='text-2xl font-bold'>Ready for Takeoff! 🚀</CardTitle>
              <CardDescription>
                Your journey to career freedom starts now
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950 dark:to-blue-950 p-6 rounded-lg'>
                <h3 className='font-semibold text-lg mb-3'>You're all set! Here's what happens next: 🎯</h3>
                <div className='space-y-3'>
                  <div className='flex items-start gap-3'>
                    <Badge variant="secondary" className='mt-1'>1</Badge>
                    <div>
                      <strong>Take the Assessment</strong> - Get your personalized career recommendations
                      (15-20 minutes of honest self-reflection)
                    </div>
                  </div>
                  <div className='flex items-start gap-3'>
                    <Badge variant="secondary" className='mt-1'>2</Badge>
                    <div>
                      <strong>Explore Your Results</strong> - Discover career paths that match your profile
                      and see what's possible
                    </div>
                  </div>
                  <div className='flex items-start gap-3'>
                    <Badge variant="secondary" className='mt-1'>3</Badge>
                    <div>
                      <strong>Plan Your Freedom Fund</strong> - Calculate how much you need and create
                      a realistic savings strategy
                    </div>
                  </div>
                  <div className='flex items-start gap-3'>
                    <Badge variant="secondary" className='mt-1'>4</Badge>
                    <div>
                      <strong>Connect & Learn</strong> - Join the community, bookmark resources,
                      and start building your network
                    </div>
                  </div>
                </div>
              </div>

              <div className='bg-blue-50 dark:bg-blue-950 p-6 rounded-lg border-l-4 border-blue-400'>
                <h4 className='font-semibold mb-2'>🎉 Welcome to the FAAFO family!</h4>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                  Remember: career change is a marathon, not a sprint. Be patient with yourself,
                  celebrate small wins, and don't be afraid to ask for help.
                  I'm here to support you (when I'm not frantically Googling error messages or fixing the latest thing I broke).
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mt-3 text-sm italic'>
                  <strong>P.S.</strong> If something breaks while you're using the platform, just remember:
                  it's not you, it's me (and my questionable coding decisions made while sleep-deprived) 🤷‍♂️
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mt-3 text-sm'>
                  <strong>P.P.S.</strong> This platform is a living, breathing (sometimes wheezing) project.
                  I'm constantly adding new features, improving existing ones, and occasionally breaking everything
                  while trying to make it better. You're not just using a tool - you're part of an ongoing experiment
                  in career liberation! 🚀
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mt-3'>
                  <strong>Want to share your story?</strong> I'd love to hear why you're here and what you're going through.
                  Sometimes it helps just to tell someone who gets it. Drop me a line at{' '}
                  <a href="mailto:<EMAIL>" className='text-blue-600 hover:text-blue-500 underline'>
                    <EMAIL>
                  </a>
                  {' '}(I actually read them all, usually while procrastinating on actual work ☕)
                </p>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed mt-2'>
                  <em>- Darjus, Solo Founder & Professional Procrastinator</em>
                </p>
              </div>

              <div className='flex justify-between items-center'>
                <Button variant='outline' onClick={() => setCurrentStep(currentStep - 1)}>
                  Back
                </Button>
                <Button onClick={handleNext} className='flex items-center gap-2 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600'>
                  Start My Journey! <Rocket className='w-4 h-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <div className='flex flex-col items-center justify-center min-h-[calc(100vh-80px)] bg-gray-50 dark:bg-gray-950 px-4 py-8'>
      <div className='w-full max-w-2xl mb-8'>
        <Progress value={(currentStep / totalSteps) * 100} className='w-full h-2' />
        <div className='flex justify-between items-center mt-3'>
          <p className='text-sm text-muted-foreground'>
            Step {currentStep} of {totalSteps}
          </p>
          <p className='text-sm text-muted-foreground'>
            {Math.round((currentStep / totalSteps) * 100)}% Complete
          </p>
        </div>
      </div>
      {getStepContent()}
    </div>
  );
}
