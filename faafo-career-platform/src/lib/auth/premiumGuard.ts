import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { SubscriptionService } from '@/lib/services/subscriptionService';

/**
 * Server-side premium access guard
 * Use this in server components and API routes
 */
export async function requirePremiumAccess(
  redirectTo: string = '/subscription',
  callbackUrl?: string
): Promise<{
  user: {
    id: string;
    email: string;
  };
  subscription: any;
}> {
  // Check authentication first
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const loginUrl = callbackUrl
      ? `/login?callbackUrl=${encodeURIComponent(callbackUrl)}`
      : '/login';
    redirect(loginUrl);
  }

  // All features are now free - no subscription check needed
  try {
    // Create mock subscription for compatibility
    const subscription = {
      tier: 'FREE',
      status: 'ACTIVE',
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      daysRemaining: 365,
      isActive: true
    };

    return {
      user: {
        id: session.user.id,
        email: session.user.email!
      },
      subscription
    };
  } catch (error) {
    console.error('Error checking premium access:', error);
    // On error, redirect to upgrade page for security
    redirect(redirectTo);
  }
}

/**
 * Check if user has premium access without redirecting
 * Returns boolean result for conditional rendering
 */
export async function checkPremiumAccess(): Promise<{
  hasAccess: boolean;
  isAuthenticated: boolean;
  user?: {
    id: string;
    email: string;
  };
  subscription?: any;
}> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return {
        hasAccess: false,
        isAuthenticated: false
      };
    }

    // All users now have free access to all features
    const subscription = {
      tier: 'FREE',
      status: 'ACTIVE',
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      daysRemaining: 365,
      isActive: true
    };

    return {
      hasAccess: true,
      isAuthenticated: true,
      user: {
        id: session.user.id,
        email: session.user.email!
      },
      subscription
    };
  } catch (error) {
    console.error('Error checking premium access:', error);
    return {
      hasAccess: false,
      isAuthenticated: false
    };
  }
}

/**
 * List of pages that require premium access
 * NOTE: All pages are now free - this list is kept for reference only
 */
export const PREMIUM_PAGES = [
  // All pages are now free for everyone
] as const;

/**
 * List of pages that are free (static content)
 */
export const FREE_PAGES = [
  '/',
  '/career-paths',
  '/resources',
  '/help',
  '/contact',
  '/subscription',
  '/auth',
  '/login',
  '/signup',
  '/privacy-policy',
  '/terms-of-service',
  '/faq'
] as const;

/**
 * Check if a page requires premium access
 * NOTE: Always returns false now - all pages are free
 */
export function isPremiumPage(pathname: string): boolean {
  // All pages are now free for everyone
  return false;
}

/**
 * Check if a page is free
 */
export function isFreePage(pathname: string): boolean {
  return FREE_PAGES.some(page => pathname === page || pathname.startsWith(`${page}/`));
}

export default {
  requirePremiumAccess,
  checkPremiumAccess,
  isPremiumPage,
  isFreePage,
  PREMIUM_PAGES,
  FREE_PAGES
};
