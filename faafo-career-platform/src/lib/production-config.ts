/**
 * Production Environment Configuration
 * Handles environment variables, validation, and production-specific settings
 */

interface ProductionConfig {
  // Database
  database: {
    url: string;
    connectionTimeout: number;
    queryTimeout: number;
    maxConnections: number;
  };

  // Authentication
  auth: {
    secret: string;
    url: string;
    sessionMaxAge: number;
  };

  // Email Service
  email: {
    apiKey?: string;
    fromAddress: string;
    provider: 'resend' | 'sendgrid' | 'console';
  };

  // Error Tracking
  errorTracking: {
    sentryDsn?: string;
    enabled: boolean;
    environment: string;
  };

  // AI Services
  ai: {
    geminiApiKey?: string;
    openaiApiKey?: string;
    enabled: boolean;
  };

  // Redis Cache
  cache: {
    redisUrl?: string;
    enabled: boolean;
    defaultTtl: number;
  };

  // Security
  security: {
    corsOrigins: string[];
    rateLimitEnabled: boolean;
    csrfEnabled: boolean;
  };

  // Monitoring
  monitoring: {
    enabled: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
  };
}

/**
 * Validate required environment variables
 */
function validateEnvironment(): void {
  const required = ['DATABASE_URL', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL'];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Get production configuration with fallbacks
 */
function getProductionConfig(): ProductionConfig {
  // Validate required variables first
  validateEnvironment();

  const isProduction = process.env['NODE_ENV'] === 'production';

  return {
    database: {
      url: process.env['DATABASE_URL']!,
      connectionTimeout: parseInt(process.env['DB_CONNECTION_TIMEOUT'] || '10000'),
      queryTimeout: parseInt(process.env['DB_QUERY_TIMEOUT'] || '5000'),
      maxConnections: parseInt(process.env['DB_MAX_CONNECTIONS'] || '10')
    },

    auth: {
      secret: process.env['NEXTAUTH_SECRET']!,
      url: process.env['NEXTAUTH_URL']!,
      sessionMaxAge: parseInt(process.env['SESSION_MAX_AGE'] || '2592000') // 30 days
    },

    email: {
      apiKey: process.env['RESEND_API_KEY'],
      fromAddress: process.env['EMAIL_FROM'] || '<EMAIL>',
      provider: process.env['RESEND_API_KEY'] ? 'resend' : 'console'
    },

    errorTracking: {
      sentryDsn: process.env['SENTRY_DSN'],
      enabled: isProduction && !!process.env['SENTRY_DSN'],
      environment: process.env['NODE_ENV'] || 'development'
    },

    ai: {
      geminiApiKey: process.env['GOOGLE_GEMINI_API_KEY'],
      openaiApiKey: process.env['OPENAI_API_KEY'],
      enabled: !!(process.env['GOOGLE_GEMINI_API_KEY'] || process.env['OPENAI_API_KEY'])
    },

    cache: {
      redisUrl: process.env['REDIS_URL'],
      enabled: !!process.env['REDIS_URL'],
      defaultTtl: parseInt(process.env['CACHE_TTL'] || '3600') // 1 hour
    },

    security: {
      corsOrigins: process.env['CORS_ORIGINS']?.split(',') || ['http://localhost:3000'],
      rateLimitEnabled: process.env['RATE_LIMIT_ENABLED'] !== 'false',
      csrfEnabled: process.env['CSRF_ENABLED'] !== 'false'
    },

    monitoring: {
      enabled: isProduction,
      logLevel: (process.env['LOG_LEVEL'] as any) || (isProduction ? 'error' : 'debug')
    }
  };
}

/**
 * Initialize production services based on configuration
 */
function initializeProductionServices(config: ProductionConfig): void {
  // Initialize Sentry if configured
  if (config.errorTracking.enabled && config.errorTracking.sentryDsn) {
    try {
      const Sentry = require('@sentry/nextjs');
      Sentry.init({
        dsn: config.errorTracking.sentryDsn,
        environment: config.errorTracking.environment,
        tracesSampleRate: 0.1,
        beforeSend(event: any) {
          // Filter out development errors in production
          if (config.errorTracking.environment === 'production') {
            // Remove sensitive data
            if (event.request?.headers) {
              delete event.request.headers.authorization;
              delete event.request.headers.cookie;
            }
          }
          return event;
        }
      });

      console.log('✅ Sentry error tracking initialized');
    } catch (error) {
      console.warn('⚠️ Failed to initialize Sentry:', error);
    }
  }

  // Log configuration status
  console.log('🚀 Production Configuration Status:');
  console.log(`   Database: ✅ Connected`);
  console.log(
    `   Email: ${config.email.provider === 'resend' ? '✅' : '⚠️'} ${config.email.provider}`
  );
  console.log(
    `   Error Tracking: ${config.errorTracking.enabled ? '✅' : '❌'} ${config.errorTracking.enabled ? 'Enabled' : 'Disabled'}`
  );
  console.log(
    `   AI Services: ${config.ai.enabled ? '✅' : '❌'} ${config.ai.enabled ? 'Enabled' : 'Disabled'}`
  );
  console.log(
    `   Cache: ${config.cache.enabled ? '✅' : '❌'} ${config.cache.enabled ? 'Redis' : 'Memory'}`
  );
  console.log(
    `   Security: ✅ Rate Limiting: ${config.security.rateLimitEnabled}, CSRF: ${config.security.csrfEnabled}`
  );
}

/**
 * Health check for production services
 */
async function healthCheck(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: Record<string, boolean>;
  timestamp: string;
}> {
  const services: Record<string, boolean> = {};

  // Check database connection
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    await prisma.$queryRaw`SELECT 1`;
    await prisma.$disconnect();
    services['database'] = true;
  } catch (error) {
    services['database'] = false;
  }

  // Check email service
  services['email'] = !!process.env['RESEND_API_KEY'];

  // Check AI services
  services['ai'] = !!(process.env['GOOGLE_GEMINI_API_KEY'] || process.env['OPENAI_API_KEY']);

  // Check cache
  services['cache'] = !!process.env['REDIS_URL'];

  // Determine overall status
  const healthyServices = Object.values(services).filter(Boolean).length;
  const totalServices = Object.keys(services).length;

  let status: 'healthy' | 'degraded' | 'unhealthy';
  if (healthyServices === totalServices) {
    status = 'healthy';
  } else if (healthyServices >= totalServices * 0.5) {
    status = 'degraded';
  } else {
    status = 'unhealthy';
  }

  return {
    status,
    services,
    timestamp: new Date().toISOString()
  };
}

// Export configuration and utilities
export const productionConfig = getProductionConfig();
export { validateEnvironment, initializeProductionServices, healthCheck };
export type { ProductionConfig };

// Auto-initialize in production
if (process.env['NODE_ENV'] === 'production') {
  try {
    initializeProductionServices(productionConfig);
  } catch (error) {
    console.error('❌ Failed to initialize production services:', error);
  }
}
