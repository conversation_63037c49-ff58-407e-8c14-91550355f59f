import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SubscriptionService } from '@/lib/services/subscriptionService';

export interface SubscriptionCheckResult {
  hasAccess: boolean;
  user?: {
    id: string;
    email: string;
  };
  subscription?: any;
  error?: string;
}

/**
 * Middleware to check if user has active subscription for premium features
 */
export async function withSubscription<PERSON>heck(
  request: NextRequest,
  handler: (request: NextRequest, checkResult: SubscriptionCheckResult) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Check authentication first
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED',
          message: 'Please sign in to access this feature'
        },
        { status: 401 }
      );
    }

    // All features are now free - no subscription check needed
    // Create a mock subscription for compatibility
    const subscription = {
      tier: 'FREE',
      status: 'ACTIVE',
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      daysRemaining: 365,
      isActive: true
    };

    const checkResult: SubscriptionCheckResult = {
      hasAccess: true,
      user: {
        id: session.user.id,
        email: session.user.email!
      },
      subscription
    };

    return await handler(request, checkResult);
  } catch (error) {
    console.error('Subscription check error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Subscription verification failed',
        code: 'SUBSCRIPTION_CHECK_ERROR',
        message: 'Unable to verify subscription status. Please try again.'
      },
      { status: 500 }
    );
  }
}

/**
 * Check subscription status without throwing errors (for conditional features)
 */
export async function checkSubscriptionStatus(userId: string): Promise<SubscriptionCheckResult> {
  // All users now have free access to all features
  const freeSubscription = {
    tier: 'FREE',
    status: 'ACTIVE',
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    daysRemaining: 365,
    isActive: true
  };

  return {
    hasAccess: true,
    subscription: freeSubscription
  };
}

/**
 * Premium feature wrapper for API routes
 */
export function requirePremiumSubscription(
  handler: (request: NextRequest, checkResult: SubscriptionCheckResult) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    return withSubscriptionCheck(request, handler);
  };
}

/**
 * Subscription status response for frontend
 */
export interface SubscriptionStatusResponse {
  hasAccess: boolean;
  subscription?: {
    tier: string;
    status: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
  };
  upgradeUrl?: string;
  message?: string;
}

/**
 * Get subscription status for frontend components
 */
export async function getSubscriptionStatusForUser(
  userId: string
): Promise<SubscriptionStatusResponse> {
  // All users now have free access to all features
  return {
    hasAccess: true,
    subscription: {
      tier: 'FREE',
      status: 'ACTIVE',
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      daysRemaining: 365,
      isActive: true
    }
  };
}

/**
 * List of API endpoints that require premium subscription
 * NOTE: All endpoints are now free - this list is kept for reference only
 */
export const PREMIUM_ENDPOINTS = [
  // All features are now free for everyone
] as const;

/**
 * Check if an endpoint requires premium subscription
 * NOTE: Always returns false now - all endpoints are free
 */
export function isPremiumEndpoint(pathname: string): boolean {
  // All endpoints are now free for everyone
  return false;
}

export default {
  withSubscriptionCheck,
  checkSubscriptionStatus,
  requirePremiumSubscription,
  getSubscriptionStatusForUser,
  isPremiumEndpoint
};
