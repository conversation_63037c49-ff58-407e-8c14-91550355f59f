// Application configuration constants
export const CONFIG = {
  // Authentication
  AUTH: {
    MAX_FAILED_ATTEMPTS: 5,
    LOCKOUT_DURATION_MS: 15 * 60 * 1000, // 15 minutes
    PASSWORD_RESET_EXPIRY_MS: 60 * 60 * 1000 // 1 hour
  },

  // Progress tracking
  PROGRESS: {
    DEFAULT_WEEKLY_GOAL: 3,
    MAX_WEEKLY_GOAL: 50,
    MIN_WEEKLY_GOAL: 1
  },

  // API limits
  API: {
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100,
    RATE_LIMIT_REQUESTS: 100,
    RATE_LIMIT_WINDOW_MS: 15 * 60 * 1000 // 15 minutes
  },

  // Assessment
  ASSESSMENT: {
    MAX_STEPS: 10,
    AUTO_SAVE_INTERVAL_MS: 30 * 1000 // 30 seconds
  },

  // Email
  EMAIL: {
    FROM_ADDRESS: process.env['EMAIL_FROM'] || '<EMAIL>',
    VERIFICATION_EXPIRY_HOURS: 24
  },

  // Database
  DATABASE: {
    CONNECTION_TIMEOUT_MS: 10000,
    QUERY_TIMEOUT_MS: 5000
  },

  // Security
  SECURITY: {
    BCRYPT_ROUNDS: 12,
    SESSION_MAX_AGE: 30 * 24 * 60 * 60 // 30 days
  },

  // All features are now completely free - no payment processing needed
} as const;

// Environment validation with security checks
export function validateEnvironment() {
  const required = ['DATABASE_URL', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL'];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Security validation - check for placeholder values
  const placeholderPatterns = [
    'your-',
    'placeholder',
    'example',
    'test-secret',
    'change-me',
    'replace-me'
  ];

  const securityIssues: string[] = [];

  // Check NEXTAUTH_SECRET
  const nextAuthSecret = process.env['NEXTAUTH_SECRET'];
  if (nextAuthSecret) {
    if (nextAuthSecret.length < 32) {
      securityIssues.push('NEXTAUTH_SECRET must be at least 32 characters long');
    }
    if (placeholderPatterns.some(pattern => nextAuthSecret.toLowerCase().includes(pattern))) {
      securityIssues.push('NEXTAUTH_SECRET appears to contain placeholder text');
    }
  }

  // Check DATABASE_URL
  const databaseUrl = process.env['DATABASE_URL'];
  if (
    databaseUrl &&
    placeholderPatterns.some(pattern => databaseUrl.toLowerCase().includes(pattern))
  ) {
    securityIssues.push('DATABASE_URL appears to contain placeholder text');
  }

  // Check API keys
  const apiKeys = [
    'RESEND_API_KEY',
    'GOOGLE_GEMINI_API_KEY',
    'SENTRY_AUTH_TOKEN'
    // Stripe keys removed - platform is now completely free
  ];
  apiKeys.forEach(key => {
    const value = process.env[key];
    if (value && placeholderPatterns.some(pattern => value.toLowerCase().includes(pattern))) {
      securityIssues.push(`${key} appears to contain placeholder text`);
    }
  });

  if (securityIssues.length > 0 && process.env['NODE_ENV'] === 'production') {
    throw new Error(`Security validation failed: ${securityIssues.join(', ')}`);
  }

  if (securityIssues.length > 0 && process.env['NODE_ENV'] !== 'test') {
    console.warn('⚠️  Environment security warnings:', securityIssues);
  }
}

// Type-safe environment access
export const ENV = {
  DATABASE_URL: process.env['DATABASE_URL']!,
  NEXTAUTH_SECRET: process.env['NEXTAUTH_SECRET']!,
  NEXTAUTH_URL: process.env['NEXTAUTH_URL']!,
  RESEND_API_KEY: process.env['RESEND_API_KEY'],
  EMAIL_FROM: process.env['EMAIL_FROM'],
  // Stripe keys removed - platform is now completely free
  NODE_ENV: process.env['NODE_ENV'] || 'development'
} as const;
