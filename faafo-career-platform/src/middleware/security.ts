/**
 * Security Middleware for Enhanced Protection
 * 
 * Implements additional security measures including:
 * - Rate limiting
 * - Request validation
 * - Security headers
 * - Attack detection
 */

import { NextRequest, NextResponse } from 'next/server';

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security configuration
const SECURITY_CONFIG = {
  // Rate limiting
  maxRequestsPerMinute: 60,
  maxRequestsPerHour: 1000,
  
  // Request size limits
  maxBodySize: 10 * 1024 * 1024, // 10MB
  maxUrlLength: 2048,
  maxHeaderSize: 8192,
  
  // Blocked patterns
  suspiciousPatterns: [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /onload\s*=/gi,
    /onerror\s*=/gi,
    /onclick\s*=/gi,
    /eval\s*\(/gi,
    /document\.cookie/gi,
    /document\.write/gi,
    /window\.location/gi
  ],
  
  // Blocked user agents
  blockedUserAgents: [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i
  ],
  
  // Allowed origins for CORS
  allowedOrigins: [
    'http://localhost:3000',
    'https://faafo-career.com',
    'https://www.faafo-career.com'
  ]
};

/**
 * Check if IP is rate limited
 */
function isRateLimited(ip: string): boolean {
  const now = Date.now();
  const key = `rate_limit_${ip}`;
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    // Reset or initialize
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + 60000 // 1 minute
    });
    return false;
  }
  
  if (current.count >= SECURITY_CONFIG.maxRequestsPerMinute) {
    return true;
  }
  
  current.count++;
  return false;
}

/**
 * Validate request for suspicious content
 */
function validateRequest(request: NextRequest): { isValid: boolean; reason?: string } {
  const url = request.url;
  const userAgent = request.headers.get('user-agent') || '';
  
  // Check URL length
  if (url.length > SECURITY_CONFIG.maxUrlLength) {
    return { isValid: false, reason: 'URL too long' };
  }
  
  // Check for suspicious patterns in URL
  for (const pattern of SECURITY_CONFIG.suspiciousPatterns) {
    if (pattern.test(url)) {
      return { isValid: false, reason: 'Suspicious pattern in URL' };
    }
  }
  
  // Check user agent
  for (const blockedPattern of SECURITY_CONFIG.blockedUserAgents) {
    if (blockedPattern.test(userAgent)) {
      return { isValid: false, reason: 'Blocked user agent' };
    }
  }
  
  // Check headers size
  let totalHeaderSize = 0;
  request.headers.forEach((value, key) => {
    totalHeaderSize += key.length + value.length;
  });
  
  if (totalHeaderSize > SECURITY_CONFIG.maxHeaderSize) {
    return { isValid: false, reason: 'Headers too large' };
  }
  
  return { isValid: true };
}

/**
 * Add security headers to response
 */
function addSecurityHeaders(response: NextResponse): NextResponse {
  // Content Security Policy - Stripe references removed
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
    "font-src 'self' https://fonts.gstatic.com; " +
    "img-src 'self' data: https: blob:; " +
    "connect-src 'self' https://vercel.live; " +
    "frame-src 'self'; " +
    "object-src 'none'; " +
    "base-uri 'self';"
  );
  
  // Additional security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // HSTS (only in production)
  if (process.env['NODE_ENV'] === 'production') {
    response.headers.set(
      'Strict-Transport-Security',
      'max-age=31536000; includeSubDomains; preload'
    );
  }
  
  return response;
}

/**
 * Log security events
 */
function logSecurityEvent(event: string, details: any) {
  const timestamp = new Date().toISOString();
  console.warn(`🚨 SECURITY EVENT [${timestamp}]: ${event}`, details);
  
  // In production, send to security monitoring service
  if (process.env['NODE_ENV'] === 'production') {
    // TODO: Integrate with security monitoring service
  }
}

/**
 * Main security middleware function
 */
export function securityMiddleware(request: NextRequest): NextResponse | null {
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const userAgent = request.headers.get('user-agent') || '';
  const url = request.url;
  
  // Skip security checks for static assets
  if (url.includes('/_next/') || url.includes('/favicon.ico')) {
    return null;
  }
  
  // Rate limiting
  if (isRateLimited(ip)) {
    logSecurityEvent('RATE_LIMIT_EXCEEDED', { ip, userAgent, url });
    return new NextResponse('Rate limit exceeded', { status: 429 });
  }
  
  // Request validation
  const validation = validateRequest(request);
  if (!validation.isValid) {
    logSecurityEvent('SUSPICIOUS_REQUEST', {
      ip,
      userAgent,
      url,
      reason: validation.reason
    });
    return new NextResponse('Request blocked', { status: 400 });
  }
  
  // Continue with request
  return null;
}

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(response: NextResponse): NextResponse {
  return addSecurityHeaders(response);
}

/**
 * Clean up rate limit store periodically
 */
export function cleanupRateLimitStore() {
  const now = Date.now();
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// Clean up every 5 minutes
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupRateLimitStore, 5 * 60 * 1000);
}
