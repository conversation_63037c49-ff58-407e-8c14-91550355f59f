import type { <PERSON>ada<PERSON> } from 'next';

import { ThemeProvider } from '@/components/layout/ThemeProvider';
import { ErrorBoundaryProvider } from '@/components/providers/ErrorBoundaryProvider';
import { AuthStateSynchronizer } from '@/components/auth/AuthStateSynchronizer';
import { SessionStateProvider } from '@/components/auth/SessionStateProvider';
import { NavigationBar } from '@/components/layout/NavigationBar';
import Footer from '@/components/layout/Footer';
import VercelAnalyticsWrapper from '@/app/components/layout/VercelAnalyticsWrapper';
import SessionWrapper from '@/components/SessionWrapper';
import { Toaster } from '@/components/ui/toaster';
import CoffeeReminderToast from '@/components/ui/CoffeeReminderToast';
import './globals.css';

export const metadata: Metadata = {
  title: 'FAAFO Career Platform - Find Your Path to Career Freedom',
  description:
    'Empowering career transitions through personalized assessments, financial planning, and community support. Take control of your career journey with FAAFO.',
  keywords: 'career, freedom, assessment, learning, professional development, career transition',
  authors: [{ name: 'FAAFO Team' }],
  robots: 'index, follow',
  openGraph: {
    title: 'FAAFO Career Platform - Find Your Path to Career Freedom',
    description:
      'Empowering career transitions through personalized assessments, financial planning, and community support.',
    type: 'website',
    locale: 'en_US'
  },
  twitter: {
    card: 'summary_large_image',
    title: 'FAAFO Career Platform',
    description: 'Find your path to career freedom'
  }
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <head>
        <meta name='theme-color' content='#1a1a1a' />
        <meta name='mobile-web-app-capable' content='yes' />
        <meta name='apple-mobile-web-app-capable' content='yes' />
        <meta name='apple-mobile-web-app-status-bar-style' content='default' />
        <meta name='format-detection' content='telephone=no' />
      </head>
      <body className='antialiased bg-background text-foreground min-h-screen flex flex-col font-sans'>
        <ThemeProvider attribute='class' defaultTheme='system' enableSystem>
          <ErrorBoundaryProvider>
            <SessionWrapper>
              <AuthStateSynchronizer>
                <SessionStateProvider>
                  {/* Skip to main content link for accessibility */}
                  <a
                    href='#main-content'
                    className='sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-gray-800 focus:text-white focus:rounded-md focus:shadow-lg'
                  >
                    Skip to main content
                  </a>

                  {/* Navigation Header */}
                  <header role='banner'>
                    <NavigationBar />
                  </header>

                  <main
                    id='main-content'
                    role='main'
                    className='flex-grow min-h-0'
                    aria-label='Main content'
                  >
                    {children}
                  </main>

                  <footer role='contentinfo'>
                    <Footer />
                  </footer>

                  {/* Toast notifications */}
                  <Toaster />

                  {/* Coffee reminder toast */}
                  <CoffeeReminderToast />
                </SessionStateProvider>
              </AuthStateSynchronizer>
            </SessionWrapper>
            <VercelAnalyticsWrapper />
          </ErrorBoundaryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
