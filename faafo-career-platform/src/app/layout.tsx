import type { <PERSON>ada<PERSON> } from 'next';

import { ThemeProvider } from '@/components/layout/ThemeProvider';
import { ErrorBoundaryProvider } from '@/components/providers/ErrorBoundaryProvider';
import { AuthStateSynchronizer } from '@/components/auth/AuthStateSynchronizer';
import { SessionStateProvider } from '@/components/auth/SessionStateProvider';
import { NavigationBar } from '@/components/layout/NavigationBar';
import Footer from '@/components/layout/Footer';
import VercelAnalyticsWrapper from '@/app/components/layout/VercelAnalyticsWrapper';
import SessionWrapper from '@/components/SessionWrapper';
import { Toaster } from '@/components/ui/toaster';
import CoffeeReminderToast from '@/components/ui/CoffeeReminderToast';
import './globals.css';

export const metadata: Metadata = {
  title: 'Free Career Change Platform | FAAFO - Find Your Dream Job & Career Path',
  description:
    'Free career assessment, skill gap analysis & interview practice. Join 10,000+ professionals who changed careers successfully. Start your career transition today!',
  keywords: 'free career assessment, career change platform, career transition help, skill gap analysis, interview practice, career path finder, professional development, career coaching online, job change guidance, career planning tool',
  authors: [{ name: 'FAAFO Career Platform Team' }],
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1
    }
  },
  openGraph: {
    title: 'Free Career Change Platform | FAAFO - Transform Your Career Today',
    description:
      'Join thousands who successfully changed careers with our free tools: career assessment, skill analysis & interview practice. Start your transformation now!',
    type: 'website',
    locale: 'en_US',
    url: 'https://faafo-career.com',
    siteName: 'FAAFO Career Platform',
    images: [
      {
        url: 'https://faafo-career.com/images/og-career-platform.jpg',
        width: 1200,
        height: 630,
        alt: 'FAAFO Career Platform - Free Career Change Tools'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Career Change Platform | FAAFO',
    description: 'Free career assessment & tools to find your dream job. Join 10,000+ successful career changers!',
    images: ['https://faafo-career.com/images/twitter-career-platform.jpg'],
    creator: '@faafo_platform'
  },
  alternates: {
    canonical: 'https://faafo-career.com'
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code'
  }
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <head>
        <meta name='theme-color' content='#1a1a1a' />
        <meta name='mobile-web-app-capable' content='yes' />
        <meta name='apple-mobile-web-app-capable' content='yes' />
        <meta name='apple-mobile-web-app-status-bar-style' content='default' />
        <meta name='format-detection' content='telephone=no' />
      </head>
      <body className='antialiased bg-background text-foreground min-h-screen flex flex-col font-sans'>
        <ThemeProvider attribute='class' defaultTheme='system' enableSystem>
          <ErrorBoundaryProvider>
            <SessionWrapper>
              <AuthStateSynchronizer>
                <SessionStateProvider>
                  {/* Skip to main content link for accessibility */}
                  <a
                    href='#main-content'
                    className='sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-gray-800 focus:text-white focus:rounded-md focus:shadow-lg'
                  >
                    Skip to main content
                  </a>

                  {/* Navigation Header */}
                  <header role='banner'>
                    <NavigationBar />
                  </header>

                  <main
                    id='main-content'
                    role='main'
                    className='flex-grow min-h-0'
                    aria-label='Main content'
                  >
                    {children}
                  </main>

                  <footer role='contentinfo'>
                    <Footer />
                  </footer>

                  {/* Toast notifications */}
                  <Toaster />

                  {/* Coffee reminder toast */}
                  <CoffeeReminderToast />
                </SessionStateProvider>
              </AuthStateSynchronizer>
            </SessionWrapper>
            <VercelAnalyticsWrapper />
          </ErrorBoundaryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
