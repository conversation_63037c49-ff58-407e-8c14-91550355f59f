import React from 'react';
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';

import prisma from '@/lib/prisma';
import { SimpleSecurity, validateFormData } from '@/lib/simple-security';
import { ValidationPipelines } from '@/lib/validation-pipeline';
import { VerificationEmail } from '@/emails/VerificationEmail';
import { sendEmail } from '@/lib/email';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.auth)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const body = await request.json();

    // Enhanced validation with comprehensive security pipeline
    const userPipeline = ValidationPipelines.createUserPipeline();
    const validationResult = await userPipeline.validate(body);

    if (!validationResult.isValid) {
      const error = new Error('Invalid input data') as any;
      error.statusCode = 400;
      error.data = { details: validationResult.errors };
      throw error;
    }

    const { email, password } = validationResult.sanitizedData;

    // Additional legacy validation for compatibility
    const validation = validateFormData(body);
    if (!validation.isValid) {
      const error = new Error('Invalid input data') as any;
      error.statusCode = 400;
      error.data = { details: validation.errors.join(', ') };
      throw error;
    }

    // Additional email validation
    const emailValidation = SimpleSecurity.validateEmail(email);
    if (!emailValidation.isValid) {
      const error = new Error(emailValidation.message) as any;
      error.statusCode = 400;
      throw error;
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() }
    });

    if (existingUser) {
      const error = new Error('An account with this email already exists. Please sign in or use a different email address.') as any;
      error.statusCode = 409;
      throw error;
    }

    // Hash the password with stronger rounds
    const hashedPassword = await bcrypt.hash(password, 12);

    // Clean email for storage (already sanitized by schema)
    const cleanEmail = email.toLowerCase().trim();

    // Create the new user (unverified)
    await prisma.user.create({
      data: {
        email: cleanEmail,
        password: hashedPassword,
        emailVerified: null // Explicitly set as unverified
      }
    });

    // Generate verification token
    const verificationToken = uuidv4();
    const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Store verification token
    await prisma.verificationToken.create({
      data: {
        identifier: cleanEmail,
        token: verificationToken,
        expires: tokenExpiry
      }
    });

    // Send verification email
    const verificationUrl = `${process.env['NEXTAUTH_URL']}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(cleanEmail)}`;

    try {
      await sendEmail({
        to: cleanEmail,
        subject: '🚀 Welcome to FAAFO! Time to verify and start the chaos',
        template: React.createElement(VerificationEmail, {
          username: cleanEmail,
          verificationLink: verificationUrl
        })
      });
      console.log(`Verification email sent to ${cleanEmail}`);
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // Don't fail the registration if email fails, but log it
    }

    return NextResponse.json(
      {
        success: true,
        data: {
          message: 'User created successfully. Please check your email to verify your account.',
          requiresVerification: true
        }
      },
      { status: 201 }
    );
  });
});
