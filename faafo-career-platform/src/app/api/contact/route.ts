import React from 'react'; // Import React
import { NextRequest, NextResponse } from 'next/server';

import { ContactFormEmail } from '@/emails/ContactFormEmail'; // Import the new email template
import { sendEmail } from '@/lib/email'; // Adjust path if necessary
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    // Apply rate limiting
    const rateLimitResult = withRateLimit(rateLimiters.api)(request);
    if (!rateLimitResult.allowed) {
      const error = new Error('Too many requests') as any;
      error.statusCode = 429;
      throw error;
    }
    const { name, email, subject, message } = await request.json();

    if (!name || !email || !subject || !message) {
      const error = new Error('All fields are required') as any;
      error.statusCode = 400;
      throw error;
    }

    // Basic email format validation (more robust validation can be added)
    if (!/^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,6}$/.test(email)) {
      const error = new Error('Invalid email address') as any;
      error.statusCode = 400;
      throw error;
    }

    await sendEmail({
      to: process.env['SUPPORT_EMAIL'] || '<EMAIL>', // Use an environment variable for support email
      subject: `🚨 FAAFO Contact: ${subject} (from ${name})`,
      template: React.createElement(ContactFormEmail, { name, email, subject, message })
    });

    return NextResponse.json(
      {
        message: 'Your message has been sent successfully!'
      },
      { status: 200 }
    );
  });
});
