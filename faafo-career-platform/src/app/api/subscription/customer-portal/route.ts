import { NextRequest, NextResponse } from 'next/server';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

interface CustomerPortalResponse {
  success: boolean;
  error?: string;
  message?: string;
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  // All features are now free - no billing portal needed
  return NextResponse.json({
    success: false,
    error: 'No billing portal needed - all features are free!',
    message: 'All features of the FAAFO Career Platform are now completely free for everyone.'
  } as CustomerPortalResponse, { status: 400 });
});
