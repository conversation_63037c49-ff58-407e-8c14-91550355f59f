import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SubscriptionService } from '@/lib/services/subscriptionService';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { prisma } from '@/lib/prisma';

interface SubscriptionStatusResponse {
  success: boolean;
  data?: {
    hasActiveSubscription: boolean;
    hasAccess: boolean; // Add this field for PremiumGuard compatibility
    subscription?: {
      id: string;
      tier: string;
      status: string;
      endDate: string;
      daysRemaining: number;
      isActive: boolean;
      package: {
        name: string;
        description: string;
        price: number;
      };
    };
    user?: {
      subscriptionStatus: string;
      subscriptionTier: string | null;
    };
  };
  error?: string;
}

// Shared handler function for both GET and POST
const handleSubscriptionStatus = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.api)(request);
  if (!rateLimitResult.allowed) {
    const error = new Error('Too many requests') as any;
    error.statusCode = 429;
    throw error;
  }

  // Check authentication
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json({
      success: false,
      error: 'Authentication required'
    } as SubscriptionStatusResponse, { status: 401 });
  }

  // All features are now free - return mock subscription data
  const subscriptionDetails = {
    id: 'free-access',
    tier: 'FREE',
    status: 'ACTIVE',
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
    daysRemaining: 365,
    isActive: true,
    package: {
      name: 'Free Access',
      description: 'All features are now free for everyone',
      price: 0
    }
  };

  return NextResponse.json({
    success: true,
    data: {
      hasActiveSubscription: true,
      hasAccess: true, // All users have access now
      subscription: subscriptionDetails,
      user: {
        subscriptionStatus: 'ACTIVE',
        subscriptionTier: 'FREE'
      }
    }
  } as SubscriptionStatusResponse);
});

// Export both GET and POST handlers
export const GET = handleSubscriptionStatus;
export const POST = handleSubscriptionStatus;
