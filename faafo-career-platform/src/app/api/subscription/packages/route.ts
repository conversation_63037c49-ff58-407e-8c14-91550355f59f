import { NextRequest, NextResponse } from 'next/server';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

interface PackagesResponse {
  success: boolean;
  data?: {
    packages: Array<{
      id: string;
      name: string;
      description: string;
      price: number;
      priceFormatted: string;
      duration: number;
      durationFormatted: string;
      tier: string;
      features: string[];
      popular: boolean;
    }>;
  };
  error?: string;
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // All features are now free - return empty packages
  return NextResponse.json({
    success: true,
    data: {
      packages: [] // No packages needed - everything is free
    },
    message: 'All features are now completely free! No subscription packages needed.'
  } as PackagesResponse);
});
