import { NextRequest, NextResponse } from 'next/server';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

interface ProcessPaymentResponse {
  success: boolean;
  error?: string;
  message?: string;
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  // All features are now free - no payment processing needed
  return NextResponse.json({
    success: false,
    error: 'Payment processing not required - all features are free!',
    message: 'All features of the FAAFO Career Platform are now completely free for everyone.'
  } as ProcessPaymentResponse, { status: 400 });
});
