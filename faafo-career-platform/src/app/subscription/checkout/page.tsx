'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Heart, ArrowRight } from 'lucide-react';

export default function CheckoutPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to subscription page since everything is free
    router.push('/subscription');
  }, [router]);

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-2xl mx-auto text-center'>
        <Card>
          <CardHeader>
            <div className='inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4 mx-auto'>
              <Heart className='h-8 w-8 text-green-600' />
            </div>
            <CardTitle className='text-2xl text-green-600'>Everything is Free!</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-gray-600 dark:text-gray-400 mb-6'>
              We've made all features of the FAAFO Career Platform completely free for everyone.
              No checkout needed!
            </p>
            <div className='space-y-3'>
              <Button asChild size='lg' className='w-full'>
                <a href='/dashboard'>
                  Go to Dashboard
                  <ArrowRight className='ml-2 h-4 w-4' />
                </a>
              </Button>
              <Button variant='outline' size='lg' asChild className='w-full'>
                <a href='/subscription'>Learn More</a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}