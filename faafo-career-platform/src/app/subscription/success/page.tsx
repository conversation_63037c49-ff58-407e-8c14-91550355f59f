'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, ArrowRight, Heart } from 'lucide-react';

export default function SuccessPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to dashboard since everything is free
    const timer = setTimeout(() => {
      router.push('/dashboard');
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-2xl mx-auto text-center'>
        <Card>
          <CardHeader>
            <div className='inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6 mx-auto'>
              <Heart className='h-10 w-10 text-green-600' />
            </div>
            <CardTitle className='text-3xl text-green-600 mb-2'>Welcome to FAAFO!</CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-lg text-gray-600 dark:text-gray-400 mb-6'>
              All features are completely free for everyone. No payment needed!
            </p>

            <div className='bg-green-50 dark:bg-green-900/20 rounded-lg p-6 mb-6'>
              <h3 className='text-lg font-semibold text-green-800 dark:text-green-200 mb-4'>
                What You Get - All Free:
              </h3>
              <div className='grid md:grid-cols-2 gap-3 text-left'>
                <div className='flex items-center space-x-2'>
                  <Check className='h-4 w-4 text-green-500 flex-shrink-0' />
                  <span className='text-sm'>AI-powered career analysis</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <Check className='h-4 w-4 text-green-500 flex-shrink-0' />
                  <span className='text-sm'>Interview practice tools</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <Check className='h-4 w-4 text-green-500 flex-shrink-0' />
                  <span className='text-sm'>Resume builder & analysis</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <Check className='h-4 w-4 text-green-500 flex-shrink-0' />
                  <span className='text-sm'>Community forum access</span>
                </div>
              </div>
            </div>

            <div className='space-y-3'>
              <Button asChild size='lg' className='w-full'>
                <a href='/dashboard'>
                  Go to Dashboard
                  <ArrowRight className='ml-2 h-4 w-4' />
                </a>
              </Button>
              <Button variant='outline' size='lg' asChild className='w-full'>
                <a href='/assessment'>Take Assessment</a>
              </Button>
            </div>

            <p className='text-sm text-gray-500 mt-4'>
              Redirecting to dashboard in 3 seconds...
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
