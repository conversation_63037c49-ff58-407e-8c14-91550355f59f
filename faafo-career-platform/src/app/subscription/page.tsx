'use client';

import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, Heart, ArrowRight } from 'lucide-react';

export default function SubscriptionPage() {
  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto text-center'>
        {/* Hero Section */}
        <div className='mb-12'>
          <div className='inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6'>
            <Heart className='h-8 w-8 text-green-600' />
          </div>
          <h1 className='text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4'>
            Everything is Free!
          </h1>
          <p className='text-xl text-gray-600 dark:text-gray-400 mb-8'>
            We've made all features of the FAAFO Career Platform completely free for everyone.
            No subscriptions, no payments, no barriers to your career growth.
          </p>
        </div>

        {/* Features Card */}
        <Card className='mb-8'>
          <CardHeader>
            <CardTitle className='text-2xl text-green-600'>What You Get - All Free</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='grid md:grid-cols-2 gap-6 text-left'>
              <div className='space-y-4'>
                <div className='flex items-start space-x-3'>
                  <Check className='h-5 w-5 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>AI-powered skills analysis and career recommendations</span>
                </div>
                <div className='flex items-start space-x-3'>
                  <Check className='h-5 w-5 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>Comprehensive career assessments and insights</span>
                </div>
                <div className='flex items-start space-x-3'>
                  <Check className='h-5 w-5 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>Interview practice with AI feedback</span>
                </div>
                <div className='flex items-start space-x-3'>
                  <Check className='h-5 w-5 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>Resume builder and analysis tools</span>
                </div>
              </div>
              <div className='space-y-4'>
                <div className='flex items-start space-x-3'>
                  <Check className='h-5 w-5 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>Community forum and networking</span>
                </div>
                <div className='flex items-start space-x-3'>
                  <Check className='h-5 w-5 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>Progress tracking and analytics</span>
                </div>
                <div className='flex items-start space-x-3'>
                  <Check className='h-5 w-5 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>Learning resources and career paths</span>
                </div>
                <div className='flex items-start space-x-3'>
                  <Check className='h-5 w-5 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>All premium tools and features</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <div className='text-center'>
          <h2 className='text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4'>
            Ready to accelerate your career?
          </h2>
          <p className='text-gray-600 dark:text-gray-400 mb-6'>
            Start using all our tools and features right now - no payment required!
          </p>
          <div className='flex flex-col sm:flex-row gap-4 justify-center'>
            <Button asChild size='lg'>
              <Link href='/dashboard'>
                Go to Dashboard
                <ArrowRight className='ml-2 h-4 w-4' />
              </Link>
            </Button>
            <Button variant='outline' size='lg' asChild>
              <Link href='/assessment'>Take Assessment</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
