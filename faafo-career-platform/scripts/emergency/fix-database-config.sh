#!/bin/bash

echo "🚨 EMERGENCY DATABASE CONFIGURATION FIX"
echo "========================================"
echo "Problem: Using remote PostgreSQL (1300ms latency)"
echo "Solution: Switch to local SQLite for development"
echo ""

# Backup current .env
echo "📋 Backing up current .env..."
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)

# Create local development .env
echo "🔧 Creating optimized local development configuration..."

cat > .env.local << 'EOF'
# Local Development Database Configuration
# Using SQLite for fast local development

# SQLite Database (Local Development)
DATABASE_URL="file:./prisma/dev.db"

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_URL_INTERNAL=http://localhost:3000
NEXTAUTH_SECRET="527380c4cddc4b535de4c6681c31bfa3="

# Email Configuration
RESEND_API_KEY=re_d4e8Qnct_JBiErHhAon7wq63CGsYqnotx
EMAIL_SERVER_USER=resend
EMAIL_SERVER_PASSWORD=re_d4e8Qnct_JBiErHhAon7wq63CGsYqnotx
EMAIL_SERVER_HOST=smtp.resend.com
EMAIL_SERVER_PORT=465
EMAIL_FROM=<EMAIL>

# AI Configuration
GOOGLE_GEMINI_API_KEY=AIzaSyBy_0j8ae75UITqKa4Z2mmTyr6a35NPKEQ
GEMINI_MODEL=Gemini 1.5 Flash
AI_CACHE_TTL=3600

# Local Redis (disabled for development)
ENABLE_REDIS=false

# Optimized Database Settings for SQLite
DB_MAX_CONNECTIONS=1
DB_CONNECTION_TIMEOUT=5000
DB_QUERY_TIMEOUT=3000
QUERY_CACHE_ENABLED=true
QUERY_CACHE_TTL=300
SLOW_QUERY_THRESHOLD=100

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Stripe (DEPRECATED - Platform is now completely free)
# STRIPE_SECRET_KEY="sk_test_51R0gt709MovY3jfsffbnxY9XOfor0odGZAinCCRTgVuwE0KGLLT2F2m3s8mpcw244eZXOyEDbpKbq5YpyA1N9AfT00veIVmMam"
# STRIPE_PUBLISHABLE_KEY="pk_test_51R0gt709MovY3jfspBFxOMhfYLTX0TIp9NjtmZC3N2jeBDm37SA6ieJ7F0jl1pDv7ZBnOvVlgEdrwQgXIEgg6QIJ00ooZwTO2a"
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51R0gt709MovY3jfspBFxOMhfYLTX0TIp9NjtmZC3N2jeBDm37SA6ieJ7F0jl1pDv7ZBnOvVlgEdrwQgXIEgg6QIJ00ooZwTO2a"

# Sentry Configuration
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509475839016960
SENTRY_ORG=darjus
SENTRY_PROJECT=javascript-nextjs
SENTRY_AUTH_TOKEN=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
EOF

echo "✅ Created .env.local with SQLite configuration"

# Update Prisma schema for SQLite
echo "🔧 Updating Prisma schema for SQLite..."

# Backup current schema
cp prisma/schema.prisma prisma/schema.prisma.backup.$(date +%Y%m%d_%H%M%S)

# Update datasource in schema
sed -i.bak 's/provider = "postgresql"/provider = "sqlite"/' prisma/schema.prisma
echo "✅ Updated Prisma schema to use SQLite"

# Reset database
echo "🗄️ Resetting database to SQLite..."
npx prisma db push --force-reset --accept-data-loss

echo ""
echo "🎯 DATABASE CONFIGURATION FIX COMPLETED!"
echo "========================================"
echo "✅ Switched from remote PostgreSQL to local SQLite"
echo "✅ Expected performance improvement: 1300ms → <50ms"
echo "✅ Database file: ./prisma/dev.db"
echo ""
echo "🔄 Next steps:"
echo "1. Restart the development server"
echo "2. Test database performance"
echo "3. Verify health check shows fast response times"
echo ""
echo "📁 Backups created:"
echo "   - .env.backup.$(date +%Y%m%d_%H%M%S)"
echo "   - prisma/schema.prisma.backup.$(date +%Y%m%d_%H%M%S)"
