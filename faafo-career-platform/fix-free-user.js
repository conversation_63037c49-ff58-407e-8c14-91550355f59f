const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixFreeUser() {
  try {
    console.log('🔧 Fixing free user subscription data...\n');

    // First, show current state
    const freeUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        subscriptions: true
      }
    });

    if (!freeUser) {
      console.log('❌ Free user not found!');
      return;
    }

    console.log('📊 BEFORE FIX:');
    console.log(`   Email: ${freeUser.email}`);
    console.log(`   Status: ${freeUser.subscriptionStatus}`);
    console.log(`   Tier: ${freeUser.subscriptionTier}`);
    console.log(`   Expires: ${freeUser.subscriptionExpiresAt}`);
    console.log(`   Subscription Records: ${freeUser.subscriptions.length}`);

    // Delete subscription records for free user
    console.log('\n🗑️ Deleting subscription records...');
    const deletedSubs = await prisma.subscription.deleteMany({
      where: { userId: freeUser.id }
    });
    console.log(`   ✅ Deleted ${deletedSubs.count} subscription records`);

    // Reset user subscription fields
    console.log('\n🔄 Resetting user subscription fields...');
    const updatedUser = await prisma.user.update({
      where: { id: freeUser.id },
      data: {
        subscriptionStatus: 'FREE',
        subscriptionTier: null,
        subscriptionExpiresAt: null,
        subscriptionStartedAt: null,
        stripeCustomerId: null // DEPRECATED: Platform is now free
      }
    });

    console.log('\n📊 AFTER FIX:');
    console.log(`   Email: ${updatedUser.email}`);
    console.log(`   Status: ${updatedUser.subscriptionStatus}`);
    console.log(`   Tier: ${updatedUser.subscriptionTier}`);
    console.log(`   Expires: ${updatedUser.subscriptionExpiresAt}`);

    // Verify the fix
    console.log('\n🔍 Verifying hasActiveSubscription logic...');
    const hasActive = updatedUser.subscriptionStatus === 'ACTIVE' && 
                     updatedUser.subscriptionExpiresAt && 
                     updatedUser.subscriptionExpiresAt > new Date();
    
    console.log(`   hasActiveSubscription: ${hasActive}`);
    console.log(`   - Status is ACTIVE: ${updatedUser.subscriptionStatus === 'ACTIVE'}`);
    console.log(`   - Has expiry date: ${!!updatedUser.subscriptionExpiresAt}`);

    console.log('\n✅ Free user fixed successfully!');
    console.log('🎯 The user should now see the payment flow instead of "You\'re All Set!"');

  } catch (error) {
    console.error('❌ Error fixing free user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixFreeUser();
