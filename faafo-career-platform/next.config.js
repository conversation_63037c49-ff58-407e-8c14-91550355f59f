// Conditionally load bundle analyzer only if it's available
let withBundleAnalyzer;
try {
  withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: process.env.ANALYZE === 'true'
  });
} catch (error) {
  // If @next/bundle-analyzer is not installed, use a no-op function
  withBundleAnalyzer = config => config;
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  // External packages configuration removed - not needed for Next.js 14.2.30

  // Webpack configuration to suppress OpenTelemetry warnings
  webpack: (config, { isServer }) => {
    // Suppress specific warnings from OpenTelemetry and require-in-the-middle
    config.ignoreWarnings = [
      // Suppress OpenTelemetry instrumentation warnings
      {
        module: /@opentelemetry\/instrumentation/,
        message: /Critical dependency: the request of a dependency is an expression/
      },
      // Suppress require-in-the-middle warnings
      {
        module: /require-in-the-middle/,
        message:
          /Critical dependency: require function is used in a way in which dependencies cannot be statically extracted/
      }
    ];

    // Bundle optimization configurations
    if (!isServer) {
      // Optimize client-side bundle
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          chunks: 'all',
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            // Separate vendor chunks for better caching
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10
            },
            // Separate UI library chunks
            ui: {
              test: /[\\/]node_modules[\\/](@radix-ui|lucide-react)[\\/]/,
              name: 'ui-libs',
              chunks: 'all',
              priority: 20
            },
            // Separate heavy libraries
            heavy: {
              test: /[\\/]node_modules[\\/](recharts|swagger-ui-react|mammoth|pdf-parse)[\\/]/,
              name: 'heavy-libs',
              chunks: 'all',
              priority: 30
            }
          }
        }
      };
    }

    return config;
  },
  // Use src directory structure
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  eslint: {
    ignoreDuringBuilds: true // Temporarily ignore due to ESLint v9 compatibility issues with Next.js config
  },
  typescript: {
    ignoreBuildErrors: false
  },
  trailingSlash: false,
  images: {
    unoptimized: true,
    domains: ['images.unsplash.com', 'via.placeholder.com']
  },

  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Content-Security-Policy',
            value:
              "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live https://va.vercel-scripts.com https://browser.sentry-cdn.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://vercel.live https://vitals.vercel-insights.com https://*.sentry.io; worker-src 'self' blob:; child-src 'self' blob:; frame-src 'self';"
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          }
        ]
      }
    ];
  }
};

module.exports = withBundleAnalyzer(nextConfig);
