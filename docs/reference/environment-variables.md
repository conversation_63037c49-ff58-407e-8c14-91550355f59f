# Environment Variables Reference

This document provides a comprehensive reference for all environment variables used in the FAAFO Career Platform.

## 🔧 Quick Setup

Copy `.env.example` to `.env.local` and configure the required variables:

```bash
cp .env.example .env.local
```

## 📋 Variable Categories

### **🔑 Required Variables**

These variables are **mandatory** for the application to function:

| Variable | Description | Example | Notes |
|----------|-------------|---------|-------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://user:pass@localhost:5432/faafo` | Required for all database operations |
| `NEXTAUTH_SECRET` | NextAuth.js secret key | `your-32-character-secret-key` | Generate with `openssl rand -base64 32` |
| `NEXTAUTH_URL` | Application base URL | `http://localhost:3000` | Must match your deployment URL |

### **🤖 AI Services (Required for AI Features)**

| Variable | Description | Example | Notes |
|----------|-------------|---------|-------|
| `GOOGLE_GEMINI_API_KEY` | Google Gemini API key | `AIzaSyC...` | Required for AI-powered features |

### **📧 Email Services (Required for Auth)**

| Variable | Description | Example | Notes |
|----------|-------------|---------|-------|
| `RESEND_API_KEY` | Resend email service API key | `re_...` | Required for email verification |
| `EMAIL_FROM` | From email address | `<EMAIL>` | Must be verified with Resend |

### **🔒 Security Configuration (Optional)**

| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `CORS_ORIGINS` | Allowed CORS origins | `http://localhost:3000` | Comma-separated list |
| `RATE_LIMIT_ENABLED` | Enable rate limiting | `true` | Set to `false` to disable |
| `CSRF_ENABLED` | Enable CSRF protection | `true` | Set to `false` to disable |
| `ENCRYPTION_KEY` | Data encryption key | Auto-generated | 32-character key for sensitive data |
| `CSRF_SECRET` | CSRF token secret | Auto-generated | Used for CSRF token generation |
| `ADMIN_EMAILS` | Admin email addresses | `<EMAIL>` | Comma-separated list |

### **📊 Monitoring & Analytics (Optional)**

| Variable | Description | Example | Notes |
|----------|-------------|---------|-------|
| `NEXT_PUBLIC_SENTRY_DSN` | Sentry error tracking DSN | `https://...@sentry.io/...` | For error monitoring |
| `SENTRY_ORG` | Sentry organization | `your-org` | Required if using Sentry |
| `SENTRY_PROJECT` | Sentry project name | `faafo-career-platform` | Required if using Sentry |

### **⚡ Performance & Caching (Optional)**

| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `REDIS_URL` | Redis connection string | `redis://localhost:6379` | Falls back to memory cache |
| `AI_CACHE_TTL` | AI response cache TTL (seconds) | `3600` | 1 hour default |
| `DATABASE_POOL_SIZE` | Database connection pool size | `10` | Adjust based on load |
| `DATABASE_TIMEOUT` | Database query timeout (ms) | `10000` | 10 seconds default |

### **🎛️ Feature Flags (Optional)**

| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `ENABLE_AI_FEATURES` | Enable AI-powered features | `true` | Requires AI API keys |
| `ENABLE_PREMIUM_FEATURES` | Enable premium features | `false` | For paid features |
| `ENABLE_ANALYTICS` | Enable analytics tracking | `true` | For usage analytics |

### **🛠️ Development Tools (Optional)**

| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `NODE_ENV` | Environment mode | `development` | `production` for live deployment |
| `LOG_LEVEL` | Logging level | `debug` | `info`, `warn`, `error` |
| `NEXT_TELEMETRY_DISABLED` | Disable Next.js telemetry | `1` | Privacy setting |

### **🧪 Testing Configuration (Optional)**

| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `PLAYWRIGHT_BASE_URL` | Base URL for E2E tests | `http://localhost:3000` | Test environment URL |
| `JEST_TIMEOUT` | Jest test timeout (ms) | `30000` | 30 seconds default |

### **📁 File Upload (Optional)**

| Variable | Description | Example | Notes |
|----------|-------------|---------|-------|
| `UPLOADTHING_SECRET` | UploadThing secret key | `sk_live_...` | For file upload features |
| `UPLOADTHING_APP_ID` | UploadThing app ID | `your-app-id` | For file upload features |

### **💳 Payment Processing (DEPRECATED)**

**IMPORTANT**: The FAAFO Career Platform is now completely free. Payment processing has been removed.

| Variable | Description | Status | Notes |
|----------|-------------|---------|-------|
| `STRIPE_SECRET_KEY` | ~~Stripe secret key~~ | ❌ Removed | No longer needed - platform is free |
| `STRIPE_PUBLISHABLE_KEY` | ~~Stripe publishable key~~ | ❌ Removed | No longer needed - platform is free |

### **🚀 CI/CD (Optional)**

| Variable | Description | Example | Notes |
|----------|-------------|---------|-------|
| `CI` | CI environment flag | `false` | Set by CI systems |
| `GITHUB_TOKEN` | GitHub API token | `ghp_...` | For deployment automation |

## 🔍 Environment Validation

The application includes built-in environment validation. Run this command to check your configuration:

```bash
npm run env:check
```

This will validate:
- ✅ Required variables are present
- ✅ Variable formats are correct
- ✅ Database connectivity
- ✅ AI service availability
- ✅ Email service configuration

## 🚨 Security Best Practices

### **Secret Management**
- Never commit `.env.local` to version control
- Use strong, unique secrets for production
- Rotate secrets regularly
- Use environment-specific configurations

### **Production Checklist**
- [ ] All required variables configured
- [ ] Strong `NEXTAUTH_SECRET` (32+ characters)
- [ ] Proper `NEXTAUTH_URL` for your domain
- [ ] Valid SSL certificates for HTTPS
- [ ] Database connection pooling configured
- [ ] Error monitoring enabled (Sentry)
- [ ] Rate limiting enabled
- [ ] CSRF protection enabled

## 🔧 Troubleshooting

### **Common Issues**

**Database Connection Failed**
```bash
Error: P1001: Can't reach database server
```
- Check `DATABASE_URL` format
- Verify database is running
- Check network connectivity

**AI Features Not Working**
```bash
Error: AI service unavailable
```
- Verify `GOOGLE_GEMINI_API_KEY` is set
- Check API key permissions
- Verify internet connectivity

**Email Verification Failed**
```bash
Error: Email service unavailable
```
- Check `RESEND_API_KEY` is valid
- Verify `EMAIL_FROM` is configured
- Ensure domain is verified with Resend

### **Debug Commands**

```bash
# Check environment variables
npm run env:check

# Test database connection
npm run db:test

# Validate AI services
npm run ai:test

# Check all services
npm run health:check
```

## 📚 Related Documentation

- [Development Setup Guide](../workflows/development-setup.md)
- [Deployment Guide](../operations/deployment-guide.md)
- [Security Configuration](./security-configuration.md)
- [API Documentation](./api-documentation.md)
